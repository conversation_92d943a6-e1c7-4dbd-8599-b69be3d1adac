'use server'
import { cookies } from 'next/headers';

const CAMPAIGN_COOKIE_NAME = 'last-active-campaign';

export async function setCampaignCookie(campaignId: string) {
  if (typeof window === 'undefined') return;
  
  document.cookie = `${CAMPAIGN_COOKIE_NAME}=${campaignId}; path=/; max-age=${60 * 60 * 24 * 30}`; // 30 days
}

export async function getCampaignCookie() {
  const cookieStore = await cookies();
  return cookieStore.get(CAMPAIGN_COOKIE_NAME)?.value;
}

export async function getCampaignFromCookie() {
  if (typeof window === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  const campaignCookie = cookies.find(cookie => cookie.trim().startsWith(`${CAMPAIGN_COOKIE_NAME}=`));
  
  if (!campaignCookie) return null;
  
  return campaignCookie.split('=')[1];
} 