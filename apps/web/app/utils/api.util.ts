import axios from 'axios';

export const getLocalApi = (
  accessToken: string = '',
  contentType = 'application/json',
) => {
  return axios.create({
    baseURL: `${process.env.NEXT_PUBLIC_SITE_URL}/api`,
    headers: {
      'Content-Type': contentType,
      ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
    },
  });
};

// export const getBackendApi = (body: any) => {
//   return axios.create({
//     baseURL: `${process.env.NEXT_PUBLIC_BACKEND_URL}/`,
//     headers: {
//       'Content-Type': 'application/json',
//       ...body
//     },
//   });
// };

export const getApi = () => {
  const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_BASE_URL || 'http://api.smartberry.ai',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // interceptor incase of error
  api.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error?.response?.data?.error) {
        return Promise.reject(error.response.data);
      }

      return Promise.reject(error);
    },
  );

  return api;
};

export const getDocupandaApi = () => {
  const api = axios.create({
    baseURL: 'https://app.docupanda.io',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': process.env.DOCUPANDA_API_KEY!,
    },
  });

  // interceptor incase of error
  api.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error?.response?.data?.error) {
        return Promise.reject(error.response.data);
      }

      return Promise.reject(error);
    },
  );

  return api;
};
