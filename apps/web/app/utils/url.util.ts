/**
 * Checks if the given string is a valid URL.
 * @param url - The URL string to validate.
 * @returns True if the URL is valid, false otherwise.
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Formats the given URL to ensure it has the correct prefix & validates it.
 * @param url - The URL string to format.
 * @returns The formatted URL.
 */
export function getFormattedUrl(url: string): string {
  if (!/^https?:\/\//i.test(url)) {
    if (/^www\./i.test(url)) {
      url = `https://${url}`;
    } else {
      url = `https://www.${url}`;
    }
  }

  if (!isValidUrl(url)) {
    throw new Error('Invalid URL');
  }

  return url;
}
