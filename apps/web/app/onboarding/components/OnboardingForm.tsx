'use client';

import { useState, useEffect, useRef, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { checkUserInvitations, completeOnboardingAction } from '../_lib/server-actions';

import {
  type OnboardingFormData,
  defaultValues,
  onboardingSchema,
} from '../schemas/onboarding';
import { WelcomeScreen } from './WelcomeScreen';
import { useUser } from '@kit/supabase/hooks/use-user';

export function OnboardingForm() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const auth = useUser();
  const invitationsCheckedRef = useRef(false);

  useEffect(() => {
    const checkInvitations = async () => {
      // Only check if we have email and haven't checked before
      if (auth?.data?.email && !invitationsCheckedRef.current) {
        invitationsCheckedRef.current = true;
        try {
          const response = await checkUserInvitations(auth.data.email);
          if (response?.hasInvitations) {
            router.push(`/join/?invite_token=${response?.invitations[0]?.invite_token}`);
          }
        } catch (error) {
          console.error('Error checking invitations:', error);
        }
      }
    };
    checkInvitations();
  }, [auth?.data?.email, router]);

  const methods = useForm<OnboardingFormData>({
    resolver: zodResolver(onboardingSchema),
    defaultValues,
    mode: 'onChange',
  });

  const onSubmit = async () => {
    const formData = methods.getValues();
    
    startTransition(async () => {
      try {
        await toast.promise(
          completeOnboardingAction(formData),
          {
            loading: 'Creating your team account...',
            success: (result) => {
              // Redirect to the team dashboard using the slug
              if (result.teamAccount?.slug) {
                router.push(`/home/<USER>/`);
              } else {
                // Fallback to general home page
                router.push('/home');
              }
              return 'Team account created successfully!';
            },
            error: 'Failed to create team account. Please try again.',
          }
        );
      } catch (error) {
        console.error('❌ Error completing onboarding:', error);
      }
    });
  };

  return (
    <div className="flex w-full flex-col">
      <div className="flex flex-1 items-center justify-center p-8 lg:p-12">
         <div className="w-full max-w-4xl">
            <FormProvider {...methods}>
                <WelcomeScreen handleNext={onSubmit} disabled={isPending} />
            </FormProvider>
          </div>
      </div>
    </div>
  );
}
